'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Wallet,
  Plus,
  CreditCard,
  History,
  DollarSign,
  ArrowUpCircle,
  ArrowDownCircle,
  ShoppingCart,
  X,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface Transacao {
  id: number
  tipo: string
  valor: number
  saldoAnterior: number
  saldoPosterior: number
  descricao: string
  data: string
}

interface WalletModalProps {
  isOpen: boolean
  onClose: () => void
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
  valorMinimo?: number // Valor mínimo necessário para finalizar aposta
  onSaldoSuficiente?: () => void // Callback quando saldo for suficiente
}

export function WalletModal({ isOpen, onClose, usuario: usuarioProp, onSaldoUpdate, valorMinimo, onSaldoSuficiente }: WalletModalProps) {
  const [usuario, setUsuario] = useState<Usuario>(usuarioProp || {
    id: 585,
    nome: 'Guilherme',
    email: '<EMAIL>',
    saldo: 0.00
  })
  
  const [transacoes, setTransacoes] = useState<Transacao[]>([])
  const [loading, setLoading] = useState(false)
  const [valorDeposito, setValorDeposito] = useState('')
  const [valorBilhete, setValorBilhete] = useState('')
  const [qrCodePix, setQrCodePix] = useState<string | null>(null)
  const [depositoLoading, setDepositoLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'deposito' | 'historico'>('deposito')
  const [depositoAtual, setDepositoAtual] = useState<any>(null)
  const [verificandoStatus, setVerificandoStatus] = useState(false)

  useEffect(() => {
    if (isOpen && usuarioProp) {
      setUsuario(usuarioProp)
      carregarDados()
    }
  }, [isOpen, usuarioProp])

  // Fechar modal com ESC
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEsc)
      // Prevenir scroll do body quando modal estiver aberto
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEsc)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // Handler para fechar ao clicar no backdrop
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const carregarDados = async () => {
    try {
      setLoading(true)
      
      // Se for modo demo, não fazer requisições reais
      if (usuario.id === 585) {
        setLoading(false)
        return
      }

      // Carregar saldo e transações reais
      const balanceResponse = await fetch(`/api/wallet/balance?user_id=${usuario.id}`)
      if (balanceResponse.ok) {
        const balanceData = await balanceResponse.json()
        if (balanceData.success) {
          setUsuario(balanceData.usuario)
          setTransacoes(balanceData.transacoes)
          onSaldoUpdate?.(balanceData.usuario.saldo)
        }
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      toast.error('Erro ao carregar dados da carteira')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const adicionarTransacao = (tipo: string, valor: number, descricao: string) => {
    const novaTransacao: Transacao = {
      id: Date.now(),
      tipo,
      valor,
      saldoAnterior: usuario.saldo,
      saldoPosterior: tipo === 'compra_bilhete' ? usuario.saldo - valor : usuario.saldo + valor,
      descricao,
      data: new Date().toLocaleString('pt-BR')
    }

    setTransacoes(prev => [novaTransacao, ...prev])
    
    // Atualizar saldo do usuário
    const novoSaldo = novaTransacao.saldoPosterior
    setUsuario(prev => ({
      ...prev,
      saldo: novoSaldo
    }))

    // Notificar componente pai sobre mudança de saldo
    onSaldoUpdate?.(novoSaldo)

    // Verificar se agora tem saldo suficiente para finalizar aposta
    if (valorMinimo && novoSaldo >= valorMinimo && onSaldoSuficiente) {
      setTimeout(() => {
        onSaldoSuficiente()
        onClose()
      }, 1000) // Pequeno delay para mostrar o sucesso
    }

    return novaTransacao
  }

  const criarDeposito = async () => {
    // Validações mais robustas
    if (!valorDeposito || valorDeposito.trim() === '') {
      toast.error('Digite um valor para o depósito')
      return
    }

    const valor = parseFloat(valorDeposito)
    if (isNaN(valor) || valor <= 0) {
      toast.error('Digite um valor válido para depósito (maior que R$ 0,00)')
      return
    }

    if (valor < 1) {
      toast.error('Valor mínimo para depósito é R$ 1,00')
      return
    }

    if (valor > 10000) {
      toast.error('Valor máximo para depósito é R$ 10.000,00')
      return
    }

    try {
      setDepositoLoading(true)
      console.log('🔄 Iniciando criação de depósito PIX...', {
        usuario_id: usuario.id,
        valor: valorDeposito
      })

      // Se for modo demo, simular
      if (usuario.id === 585) {
        setTimeout(() => {
          const qrCodeBase64 = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkNvZGlnbyBQSVg8L3RleHQ+PC9zdmc+"
          setQrCodePix(qrCodeBase64)
          setDepositoAtual({
            transaction_id: `DEMO_${Date.now()}`,
            order_id: `ORDER_${Date.now()}`,
            valor: parseFloat(valorDeposito)
          })
          toast.success('QR Code PIX gerado! Clique em "Confirmar Pagamento" para simular')
          setDepositoLoading(false)
        }, 1000)
        return
      }

      // Criar depósito real
      const response = await fetch('/api/wallet/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: usuario.id,
          valor: parseFloat(valorDeposito),
          client_name: usuario.nome,
          client_email: usuario.email,
          client_document: '12345678901'
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('📥 Resposta da API de depósito:', data)

      if (data.success && data.deposito) {
        toast.success('Depósito PIX criado com sucesso!')

        // Verificar se temos QR Code
        if (data.deposito.qrcode_image) {
          setQrCodePix(data.deposito.qrcode_image)
        } else {
          console.warn('⚠️ QR Code não retornado pela API')
          toast.warning('QR Code não foi gerado. Tente novamente.')
        }

        setDepositoAtual(data.deposito)
        carregarDados()

        // Iniciar verificação automática de status
        if (data.deposito.transaction_id) {
          iniciarVerificacaoStatus(data.deposito.transaction_id)
        }
      } else {
        console.error('❌ Erro na resposta da API:', data)
        toast.error(data.error || 'Erro ao criar depósito')
      }

    } catch (error) {
      console.error('❌ Erro ao criar depósito:', error)
      toast.error(`Erro ao criar depósito PIX: ${error.message}`)
    } finally {
      setDepositoLoading(false)
    }
  }

  // Função para verificar status do depósito automaticamente
  const iniciarVerificacaoStatus = (transactionId: string) => {
    console.log('🔄 Iniciando verificação automática de status para:', transactionId)

    const verificarStatus = async () => {
      try {
        setVerificandoStatus(true)

        const response = await fetch(`/api/wallet/webhook?transaction_id=${transactionId}`)
        if (response.ok) {
          const data = await response.json()

          if (data.success && data.deposito.status === 'pago') {
            console.log('✅ Depósito confirmado automaticamente!')
            toast.success('🎉 Depósito confirmado! Saldo creditado na carteira.')

            // Atualizar dados do usuário
            carregarDados()

            // Notificar componente pai sobre atualização do saldo
            if (onSaldoUpdate) {
              onSaldoUpdate(data.deposito.valor + usuario.saldo)
            }

            // Verificar se saldo é suficiente para finalizar aposta
            if (valorMinimo && (data.deposito.valor + usuario.saldo) >= valorMinimo) {
              toast.success('Saldo suficiente! Você pode finalizar sua aposta.')
              if (onSaldoSuficiente) {
                setTimeout(() => {
                  onSaldoSuficiente()
                }, 2000)
              }
            }

            // Limpar QR Code e depósito atual
            setQrCodePix(null)
            setDepositoAtual(null)
            setValorDeposito('')

            return true // Parar verificação
          }
        }

        return false // Continuar verificando
      } catch (error) {
        console.error('❌ Erro ao verificar status:', error)
        return false
      } finally {
        setVerificandoStatus(false)
      }
    }

    // Verificar a cada 5 segundos por até 10 minutos
    let tentativas = 0
    const maxTentativas = 120 // 10 minutos

    const interval = setInterval(async () => {
      tentativas++

      const confirmado = await verificarStatus()

      if (confirmado || tentativas >= maxTentativas) {
        clearInterval(interval)
        if (tentativas >= maxTentativas) {
          console.log('⏰ Timeout na verificação automática de depósito')
          setVerificandoStatus(false)
        }
      }
    }, 5000)

    // Cleanup quando o modal for fechado
    return () => clearInterval(interval)
  }

  const confirmarPagamento = async () => {
    if (!depositoAtual) {
      toast.error('Nenhum depósito ativo para confirmar')
      return
    }

    try {
      console.log('🔄 Simulando confirmação de pagamento via webhook...')

      // Simular webhook de confirmação
      const response = await fetch('/api/wallet/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transaction_id: depositoAtual.transaction_id,
          order_id: depositoAtual.order_id,
          status: 'PAID',
          value: depositoAtual.valor,
          end_to_end_id: `E${Date.now()}${Math.random().toString(36).substr(2, 9)}`
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('🎉 Pagamento confirmado! Saldo creditado.')

        // Recarregar dados
        carregarDados()

        // Atualizar saldo no componente pai
        const novoSaldo = usuario.saldo + depositoAtual.valor
        onSaldoUpdate?.(novoSaldo)

        // Verificar se saldo é suficiente
        if (valorMinimo && novoSaldo >= valorMinimo) {
          toast.success('Saldo suficiente! Você pode finalizar sua aposta.')
          if (onSaldoSuficiente) {
            setTimeout(() => {
              onSaldoSuficiente()
            }, 2000)
          }
        }

        // Limpar estado
        setQrCodePix(null)
        setDepositoAtual(null)
        setValorDeposito('')
      } else {
        toast.error(data.error || 'Erro ao confirmar pagamento')
      }
    } catch (error) {
      console.error('❌ Erro ao confirmar pagamento:', error)
      toast.error('Erro ao confirmar pagamento')
    }
  }

  const comprarBilhete = () => {
    if (!valorBilhete || parseFloat(valorBilhete) <= 0) {
      toast.error('Digite um valor válido para o bilhete')
      return
    }

    const valor = parseFloat(valorBilhete)
    
    if (usuario.saldo < valor) {
      toast.error(`Saldo insuficiente! Saldo atual: ${formatCurrency(usuario.saldo)}`)
      return
    }

    adicionarTransacao('compra_bilhete', valor, `Compra de bilhete - BLT${Date.now()}`)
    toast.success(`Bilhete de ${formatCurrency(valor)} comprado com sucesso!`)
    setValorBilhete('')
  }



  const getTransactionIcon = (tipo: string) => {
    switch (tipo) {
      case 'deposito':
        return <ArrowUpCircle className="h-4 w-4 text-green-600" />
      case 'compra_bilhete':
        return <ArrowDownCircle className="h-4 w-4 text-red-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    }
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header do Modal */}
        <div className="flex items-center justify-between p-6 border-b bg-slate-800 text-white">
          <div className="flex items-center space-x-3">
            <Wallet className="h-6 w-6 text-blue-400" />
            <div>
              <h2 className="text-xl font-bold">Carteira Digital</h2>
              <p className="text-sm text-gray-300">{usuario.nome} - Saldo: {formatCurrency(usuario.saldo)}</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              console.log('🔄 Fechando modal da carteira...')
              onClose()
            }}
            className="text-white hover:bg-slate-700 transition-colors"
            title="Fechar carteira"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('deposito')}
            className={`flex-1 px-6 py-3 text-sm font-medium ${
              activeTab === 'deposito'
                ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Plus className="h-4 w-4 inline mr-2" />
            Adicionar Saldo
          </button>
          <button
            onClick={() => setActiveTab('historico')}
            className={`flex-1 px-6 py-3 text-sm font-medium ${
              activeTab === 'historico'
                ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <History className="h-4 w-4 inline mr-2" />
            Histórico
          </button>
        </div>

        {/* Conteúdo do Modal */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {activeTab === 'deposito' && (
            <div className="space-y-6">
              {/* Alerta de valor mínimo necessário */}
              {valorMinimo && usuario.saldo < valorMinimo && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <div className="text-yellow-600">⚠️</div>
                    <div>
                      <p className="text-sm font-medium text-yellow-800">
                        Saldo insuficiente para finalizar aposta
                      </p>
                      <p className="text-sm text-yellow-700">
                        Você precisa de <strong>{formatCurrency(valorMinimo - usuario.saldo)}</strong> a mais para finalizar sua aposta.
                      </p>
                    </div>
                  </div>
                </div>
              )}
              {/* Ações Rápidas */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <Input
                        type="number"
                        placeholder="Valor do bilhete (R$)"
                        value={valorBilhete}
                        onChange={(e) => setValorBilhete(e.target.value)}
                        min="0.01"
                        step="0.01"
                      />
                      <Button
                        onClick={comprarBilhete}
                        className="flex items-center space-x-2"
                      >
                        <ShoppingCart className="h-4 w-4" />
                        <span>Comprar</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Saldo Atual</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {formatCurrency(usuario.saldo)}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Depósito PIX */}
              <Card className="border-blue-200 bg-blue-50">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2 text-green-600" />
                    Depósito via PIX
                  </CardTitle>
                  <CardDescription>
                    Adicione saldo à sua carteira através de PIX
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex space-x-4">
                    <div className="flex-1">
                      <Input
                        type="number"
                        placeholder="Valor do depósito (R$)"
                        value={valorDeposito}
                        onChange={(e) => {
                          const value = e.target.value
                          // Permitir apenas números e ponto decimal
                          if (value === '' || /^\d*\.?\d*$/.test(value)) {
                            setValorDeposito(value)
                          }
                        }}
                        min="1"
                        max="10000"
                        step="0.01"
                        className="text-right"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            criarDeposito()
                          }
                        }}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Valor entre R$ 1,00 e R$ 10.000,00
                      </p>
                    </div>
                    <Button
                      onClick={criarDeposito}
                      disabled={depositoLoading || !valorDeposito || parseFloat(valorDeposito) < 1}
                      className="flex items-center space-x-2 whitespace-nowrap"
                    >
                      <CreditCard className="h-4 w-4" />
                      <span>{depositoLoading ? 'Gerando...' : 'Gerar PIX'}</span>
                    </Button>
                  </div>

                  {qrCodePix && (
                    <div className="mt-4 p-4 bg-white rounded-lg text-center border">
                      <p className="text-sm text-gray-600 mb-2">QR Code PIX gerado:</p>
                      <div className="w-32 h-32 mx-auto mb-4 border rounded">
                        <img
                          src={qrCodePix}
                          alt="QR Code PIX"
                          className="w-full h-full object-contain"
                        />
                      </div>

                      {/* Indicador de verificação automática */}
                      {verificandoStatus && (
                        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="flex items-center justify-center space-x-2">
                            <Clock className="h-4 w-4 text-blue-600 animate-spin" />
                            <span className="text-sm text-blue-700">
                              Verificando pagamento automaticamente...
                            </span>
                          </div>
                          <p className="text-xs text-blue-600 mt-1">
                            O sistema está monitorando seu pagamento em tempo real
                          </p>
                        </div>
                      )}

                      <div className="space-y-2">
                        <Button
                          onClick={confirmarPagamento}
                          className="bg-green-600 hover:bg-green-700 w-full"
                          disabled={verificandoStatus}
                        >
                          {verificandoStatus ? 'Aguardando...' : 'Simular Pagamento Confirmado'}
                        </Button>

                        <p className="text-xs text-gray-500">
                          💡 O pagamento será confirmado automaticamente quando detectado
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'historico' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Histórico de Transações</h3>
              
              {transacoes.length === 0 ? (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Nenhuma transação ainda. Faça um depósito ou adicione um bônus para começar!
                  </p>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {transacoes.map((transacao) => (
                    <div key={transacao.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getTransactionIcon(transacao.tipo)}
                        <div>
                          <p className="font-medium text-gray-900">{transacao.descricao}</p>
                          <p className="text-sm text-gray-500">{transacao.data}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-bold ${
                          transacao.tipo === 'compra_bilhete' ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {transacao.tipo === 'compra_bilhete' ? '-' : '+'}
                          {formatCurrency(transacao.valor)}
                        </p>
                        <p className="text-xs text-gray-500">
                          Saldo: {formatCurrency(transacao.saldoPosterior)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
