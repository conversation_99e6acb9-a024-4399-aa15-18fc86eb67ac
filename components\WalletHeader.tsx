'use client'

import { useState, useEffect, useRef } from 'react'
import { ConfigModal } from './ConfigModal'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Plus,
  User,
  Clock,
  DollarSign,
  LogOut,
  Settings,
  ChevronDown
} from 'lucide-react'
import { WalletModal } from './WalletModal'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface WalletHeaderProps {
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
  onSearchClick?: () => void
  onLogout?: () => void
}

export function WalletHeader({ usuario, onSaldoUpdate, onSearchClick, onLogout }: WalletHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showConfigModal, setShowConfigModal] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const mobileMenuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Fechar menu quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showUserMenu) {
        const isClickInsideDesktopMenu = menuRef.current && menuRef.current.contains(event.target as Node)
        const isClickInsideMobileMenu = mobileMenuRef.current && mobileMenuRef.current.contains(event.target as Node)

        if (!isClickInsideDesktopMenu && !isClickInsideMobileMenu) {
          setShowUserMenu(false)
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu])

  const formatDateTime = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getInitials = (nome: string) => {
    return nome
      .split(' ')
      .map(n => n.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  return (
    <header className="wallet-header border-b border-slate-700 shadow-lg sticky top-0 z-40 overflow-visible">
      <div className="w-full max-w-none mx-auto">
        {/* Desktop Layout */}
        <div className="hidden lg:flex items-center justify-between p-4 gap-4">
          {/* Left Section - Data e Hora */}
          <div className="flex items-center space-x-3 min-w-0 flex-shrink-0">
            <Clock className="h-4 w-4 text-yellow-400" />
            <div className="text-white">
              <div className="text-sm font-medium text-yellow-400 whitespace-nowrap">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-gray-300">UTC -03:00</div>
            </div>
          </div>

          {/* Center Section - Saldo e Depósito */}
          <div className="flex items-center space-x-3 flex-shrink-0">
            {/* Saldo */}
            <div className="balance-card border border-slate-600 rounded-lg px-3 py-2 flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-400" />
              <span className="text-white font-bold text-base">
                {formatCurrency(usuario?.saldo || 0)}
              </span>
            </div>

            {/* Botão Depósito */}
            <Button
              onClick={() => setIsWalletModalOpen(true)}
              className="deposit-button text-white px-4 py-2 rounded-lg font-medium flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Depósito</span>
            </Button>
          </div>

          {/* Right Section - Usuário */}
          <div className="flex items-center space-x-3 min-w-0 flex-shrink-0">
            {usuario ? (
              <div className="flex items-center space-x-3">
                {/* User Info */}
                <div className="text-right">
                  <div className="text-sm font-medium text-white text-truncate-mobile">
                    {usuario.nome}
                  </div>
                  <div className="text-xs text-gray-300">
                    ID: {usuario.id}
                  </div>
                </div>

                {/* User Menu */}
                <div className="relative overflow-visible" ref={menuRef}>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation()
                      setShowUserMenu(!showUserMenu)
                    }}
                    variant="ghost"
                    className="p-1 hover:bg-slate-700 rounded-full transition-colors flex items-center space-x-1"
                  >
                    <div className="user-avatar w-8 h-8 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xs">
                        {getInitials(usuario.nome)}
                      </span>
                    </div>
                    <ChevronDown className="h-3 w-3 text-gray-300" />
                  </Button>

                  {/* Dropdown Menu */}
                  {showUserMenu && (
                    <div className="user-menu-dropdown absolute right-0 mt-2 w-44 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-[9999]">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowUserMenu(false)
                          onSearchClick?.()
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <Search className="h-4 w-4" />
                        <span>Buscar</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowUserMenu(false)
                          setShowConfigModal(true)
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <Settings className="h-4 w-4" />
                        <span>Configurações</span>
                      </button>
                      <hr className="my-1" />
                      <button
                        onClick={() => {
                          console.log('🚀 BOTÃO SAIR CLICADO!')
                          setShowUserMenu(false)
                          if (onLogout) {
                            onLogout()
                          } else {
                            // Fallback - logout direto
                            localStorage.clear()
                            window.location.reload()
                          }
                        }}
                        className="w-full px-3 py-2 text-left text-red-600 hover:bg-red-50 flex items-center space-x-2 text-sm transition-colors"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Sair</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Button
                  onClick={() => window.location.href = '/login'}
                  variant="outline"
                  size="sm"
                  className="text-white border-white hover:bg-white hover:text-slate-800"
                >
                  Faça login
                </Button>
                <Button
                  onClick={() => window.location.href = '/registro'}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  Criar conta
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Tablet Layout */}
        <div className="hidden md:flex lg:hidden items-center justify-between p-3 gap-3">
          {/* Left - Time */}
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-yellow-400" />
            <div className="text-white">
              <div className="text-xs font-medium text-yellow-400">
                {currentTime.toLocaleTimeString('pt-BR')}
              </div>
              <div className="text-xs text-gray-300">
                {currentTime.toLocaleDateString('pt-BR')}
              </div>
            </div>
          </div>

          {/* Center - Balance and Deposit */}
          <div className="flex items-center space-x-2">
            <div className="balance-card border border-slate-600 rounded-lg px-3 py-1.5 flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-400" />
              <span className="text-white font-bold text-sm">
                {formatCurrency(usuario?.saldo || 0)}
              </span>
            </div>
            <Button
              onClick={() => setIsWalletModalOpen(true)}
              size="sm"
              className="deposit-button text-white px-3 py-1.5"
            >
              <Plus className="h-4 w-4 mr-1" />
              Depósito
            </Button>
          </div>

          {/* Right - User */}
          {usuario && (
            <div className="flex items-center space-x-2">
              <div className="text-right">
                <div className="text-xs font-medium text-white text-truncate-mobile">
                  {usuario.nome}
                </div>
                <div className="text-xs text-gray-300">ID: {usuario.id}</div>
              </div>
              <div className="relative overflow-visible" ref={mobileMenuRef}>
                <Button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  variant="ghost"
                  size="sm"
                  className="p-1 hover:bg-slate-700 rounded-full"
                >
                  <div className="user-avatar w-7 h-7 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-xs">
                      {getInitials(usuario.nome)}
                    </span>
                  </div>
                  <ChevronDown className="h-3 w-3 text-gray-300 ml-1" />
                </Button>

                {showUserMenu && (
                  <div className="user-menu-dropdown absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-[9999]">
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        onSearchClick?.()
                      }}
                      className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                    >
                      <Search className="h-4 w-4" />
                      <span>Buscar</span>
                    </button>
                    <button
                      onClick={() => {
                        console.log('🚀 BOTÃO SAIR CLICADO!')
                        setShowUserMenu(false)
                        if (onLogout) {
                          onLogout()
                        } else {
                          // Fallback - logout direto
                          localStorage.clear()
                          window.location.reload()
                        }
                      }}
                      className="w-full px-3 py-2 text-left text-red-600 hover:bg-red-50 flex items-center space-x-2 text-sm transition-colors"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Sair</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => window.location.href = '/login'}
                variant="outline"
                size="sm"
                className="text-white border-white hover:bg-white hover:text-slate-800"
              >
                Login
              </Button>
              <Button
                onClick={() => window.location.href = '/registro'}
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Registro
              </Button>
            </div>
          )}
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden">
          {/* Single Row Layout */}
          <div className="flex items-center justify-between p-3 gap-2">
            {/* Left - Time */}
            <div className="flex items-center space-x-2 flex-shrink-0">
              <Clock className="h-4 w-4 text-yellow-400" />
              <div className="text-white">
                <div className="text-xs font-medium text-yellow-400">
                  {currentTime.toLocaleTimeString('pt-BR', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
                <div className="text-xs text-gray-300">
                  {currentTime.toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: '2-digit'
                  })}
                </div>
              </div>
            </div>

            {/* Center - Balance */}
            <div className="balance-card border border-slate-600 rounded-lg px-2.5 py-1.5 flex items-center space-x-1.5 flex-shrink-0">
              <DollarSign className="h-4 w-4 text-green-400" />
              <span className="text-white font-bold text-sm">
                {formatCurrency(usuario?.saldo || 0)}
              </span>
            </div>

            {/* Right - Actions */}
            <div className="flex items-center space-x-1.5 flex-shrink-0">
              <Button
                onClick={() => setIsWalletModalOpen(true)}
                size="sm"
                className="deposit-button text-white px-3 py-1.5 text-xs"
              >
                <Plus className="h-3 w-3 mr-1" />
                Depósito
              </Button>

              {usuario && (
                <div className="relative">
                  <Button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-slate-700 p-1.5 flex items-center space-x-1"
                  >
                    <div className="user-avatar w-6 h-6 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xs">
                        {getInitials(usuario.nome)}
                      </span>
                    </div>
                    <ChevronDown className="h-3 w-3" />
                  </Button>

                  {/* Mobile Dropdown */}
                  {showUserMenu && (
                    <div className="user-menu-dropdown absolute right-0 mt-2 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                      <div className="px-3 py-2 border-b border-gray-100">
                        <div className="text-xs font-medium text-gray-900 truncate">
                          {usuario.nome}
                        </div>
                        <div className="text-xs text-gray-500">ID: {usuario.id}</div>
                      </div>
                      <button
                        onClick={() => {
                          setShowUserMenu(false)
                          onSearchClick?.()
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <Search className="h-4 w-4" />
                        <span>Buscar</span>
                      </button>
                      <button
                        onClick={() => {
                          console.log('🚀 BOTÃO SAIR CLICADO!')
                          setShowUserMenu(false)
                          if (onLogout) {
                            onLogout()
                          } else {
                            // Fallback - logout direto
                            localStorage.clear()
                            window.location.reload()
                          }
                        }}
                        className="w-full px-3 py-2 text-left text-red-600 hover:bg-red-50 flex items-center space-x-2 text-sm transition-colors"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Sair</span>
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center space-x-1">
                  <Button
                    onClick={() => window.location.href = '/login'}
                    variant="outline"
                    size="sm"
                    className="text-white border-white hover:bg-white hover:text-slate-800 text-xs px-2 py-1"
                  >
                    Login
                  </Button>
                  <Button
                    onClick={() => window.location.href = '/registro'}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white text-xs px-2 py-1"
                  >
                    Registro
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Modal da Carteira */}
      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
        usuario={usuario}
        onSaldoUpdate={onSaldoUpdate}
      />

      {/* Modal de Configurações */}
      <ConfigModal
        isOpen={showConfigModal}
        onClose={() => setShowConfigModal(false)}
        usuario={usuario}
      />
    </header>
  )
}

// Componente de Status Badge para diferentes estados
export function StatusBadge({ status }: { status: string }) {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pago':
        return { color: 'bg-green-500', text: 'Pago' }
      case 'pendente':
        return { color: 'bg-yellow-500', text: 'Pendente' }
      case 'expirado':
        return { color: 'bg-red-500', text: 'Expirado' }
      case 'cancelado':
        return { color: 'bg-gray-500', text: 'Cancelado' }
      default:
        return { color: 'bg-gray-500', text: status }
    }
  }

  const config = getStatusConfig(status)

  return (
    <Badge className={`${config.color} text-white text-xs px-2 py-1`}>
      {config.text}
    </Badge>
  )
}
