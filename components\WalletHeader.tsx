'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Plus,
  User,
  Clock,
  DollarSign
} from 'lucide-react'
import { WalletModal } from './WalletModal'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface WalletHeaderProps {
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
  onSearchClick?: () => void
}

export function WalletHeader({ usuario, onSaldoUpdate, onSearchClick }: WalletHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatDateTime = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getInitials = (nome: string) => {
    return nome
      .split(' ')
      .map(n => n.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  return (
    <header className="bg-slate-800 border-b border-slate-700 p-3 sm:p-4">
      <div className="w-full max-w-none mx-auto flex items-center justify-between">
        {/* Data e Hora */}
        <div className="flex items-center space-x-2 text-white">
          <Clock className="h-4 w-4 text-gray-300" />
          <div className="text-sm font-medium">
            <div className="text-yellow-400">{formatDateTime(currentTime)}</div>
            <div className="text-xs text-gray-300">UTC -03:00</div>
          </div>
        </div>

        {/* Saldo e Botões Centrais */}
        <div className="flex items-center space-x-4">
          {/* Saldo */}
          <div className="flex items-center space-x-2">
            <div className="bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-400" />
              <span className="text-white font-bold text-sm">
                {formatCurrency(usuario?.saldo || 0)}
              </span>
            </div>
          </div>

          {/* Botão Depósito */}
          <Button
            onClick={() => setIsWalletModalOpen(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Depósito</span>
          </Button>
        </div>

        {/* Usuário e Busca */}
        <div className="flex items-center space-x-3">
          {/* Informações do Usuário */}
          {usuario && (
            <div className="flex items-center space-x-3">
              {/* Avatar */}
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {getInitials(usuario.nome)}
                </span>
              </div>
              
              {/* Info do Usuário */}
              <div className="text-white">
                <div className="text-sm font-medium">{usuario.nome}</div>
                <div className="text-xs text-gray-300 flex items-center space-x-1">
                  <span>ID: {usuario.id}</span>
                </div>
              </div>
            </div>
          )}

          {/* Botão de Busca */}
          <Button
            onClick={onSearchClick}
            variant="ghost"
            size="icon"
            className="text-white hover:bg-slate-700 rounded-full"
          >
            <Search className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Versão Mobile - Layout Responsivo */}
      <div className="md:hidden mt-3 pt-3 border-t border-slate-700">
        <div className="flex items-center justify-between">
          {/* Saldo Mobile */}
          <div className="flex items-center space-x-2">
            <DollarSign className="h-4 w-4 text-green-400" />
            <span className="text-white font-bold text-lg">
              {formatCurrency(usuario?.saldo || 0)}
            </span>
          </div>

          {/* Botões Mobile */}
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => setIsWalletModalOpen(true)}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Plus className="h-4 w-4 mr-1" />
              Depósito
            </Button>
            
            <Button
              onClick={onSearchClick}
              variant="ghost"
              size="icon"
              className="text-white hover:bg-slate-700"
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Modal da Carteira */}
      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
        usuario={usuario}
        onSaldoUpdate={onSaldoUpdate}
      />
    </header>
  )
}

// Componente de Status Badge para diferentes estados
export function StatusBadge({ status }: { status: string }) {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pago':
        return { color: 'bg-green-500', text: 'Pago' }
      case 'pendente':
        return { color: 'bg-yellow-500', text: 'Pendente' }
      case 'expirado':
        return { color: 'bg-red-500', text: 'Expirado' }
      case 'cancelado':
        return { color: 'bg-gray-500', text: 'Cancelado' }
      default:
        return { color: 'bg-gray-500', text: status }
    }
  }

  const config = getStatusConfig(status)

  return (
    <Badge className={`${config.color} text-white text-xs px-2 py-1`}>
      {config.text}
    </Badge>
  )
}
