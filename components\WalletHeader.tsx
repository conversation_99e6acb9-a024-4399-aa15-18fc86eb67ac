'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Plus,
  User,
  Clock,
  DollarSign,
  LogOut,
  Settings,
  ChevronDown
} from 'lucide-react'
import { WalletModal } from './WalletModal'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface WalletHeaderProps {
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
  onSearchClick?: () => void
  onLogout?: () => void
}

export function WalletHeader({ usuario, onSaldoUpdate, onSearchClick, onLogout }: WalletHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Fechar menu quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showUserMenu) {
        setShowUserMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu])

  const formatDateTime = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getInitials = (nome: string) => {
    return nome
      .split(' ')
      .map(n => n.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  return (
    <header className="wallet-header border-b border-slate-700 shadow-lg">
      <div className="w-full max-w-none mx-auto">
        {/* Desktop Layout */}
        <div className="hidden md:flex items-center justify-between p-4">
          {/* Left Section - Data e Hora */}
          <div className="flex items-center space-x-3 min-w-0 flex-1">
            <Clock className="h-5 w-5 text-yellow-400 flex-shrink-0" />
            <div className="text-white min-w-0">
              <div className="text-sm font-semibold text-yellow-400 truncate">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-gray-300">UTC -03:00</div>
            </div>
          </div>

          {/* Center Section - Saldo e Depósito */}
          <div className="flex items-center space-x-4 flex-shrink-0">
            {/* Saldo */}
            <div className="balance-card border border-slate-600 rounded-lg px-4 py-2.5 flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-400" />
              <span className="text-white font-bold text-lg">
                {formatCurrency(usuario?.saldo || 0)}
              </span>
            </div>

            {/* Botão Depósito */}
            <Button
              onClick={() => setIsWalletModalOpen(true)}
              className="deposit-button text-white px-6 py-2.5 rounded-lg font-semibold flex items-center space-x-2"
            >
              <Plus className="h-5 w-5" />
              <span>Depósito</span>
            </Button>
          </div>

          {/* Right Section - Usuário e Ações */}
          <div className="flex items-center space-x-3 min-w-0 flex-1 justify-end">
            {usuario ? (
              <div className="flex items-center space-x-3">
                {/* User Info */}
                <div className="text-right min-w-0">
                  <div className="text-sm font-semibold text-white truncate">
                    {usuario.nome}
                  </div>
                  <div className="text-xs text-gray-300">
                    ID: {usuario.id}
                  </div>
                </div>

                {/* User Menu */}
                <div className="relative">
                  <Button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    variant="ghost"
                    className="p-2 hover:bg-slate-700 rounded-full transition-colors"
                  >
                    <div className="user-avatar w-10 h-10 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">
                        {getInitials(usuario.nome)}
                      </span>
                    </div>
                    <ChevronDown className="h-4 w-4 text-gray-300 ml-1" />
                  </Button>

                  {/* Dropdown Menu */}
                  {showUserMenu && (
                    <div className="user-menu-dropdown absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                      <button
                        onClick={() => {
                          setShowUserMenu(false)
                          onSearchClick?.()
                        }}
                        className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                      >
                        <Search className="h-4 w-4" />
                        <span>Buscar</span>
                      </button>
                      <button
                        onClick={() => {
                          setShowUserMenu(false)
                          // TODO: Implementar configurações
                        }}
                        className="w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                      >
                        <Settings className="h-4 w-4" />
                        <span>Configurações</span>
                      </button>
                      <hr className="my-1" />
                      <button
                        onClick={() => {
                          setShowUserMenu(false)
                          onLogout?.()
                        }}
                        className="w-full px-4 py-2 text-left text-red-600 hover:bg-red-50 flex items-center space-x-2"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Sair</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-gray-400 text-sm">
                Faça login para acessar sua carteira
              </div>
            )}
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden p-3">
          {/* Top Row - User and Time */}
          <div className="flex items-center justify-between mb-3">
            {/* User Info Mobile */}
            {usuario ? (
              <div className="flex items-center space-x-3">
                <div className="user-avatar w-8 h-8 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-xs">
                    {getInitials(usuario.nome)}
                  </span>
                </div>
                <div className="text-white">
                  <div className="text-sm font-semibold truncate max-w-32">
                    {usuario.nome}
                  </div>
                  <div className="text-xs text-gray-300">ID: {usuario.id}</div>
                </div>
              </div>
            ) : (
              <div className="text-gray-400 text-sm">Não logado</div>
            )}

            {/* Time Mobile */}
            <div className="text-right">
              <div className="text-xs text-yellow-400 font-semibold">
                {currentTime.toLocaleTimeString('pt-BR')}
              </div>
              <div className="text-xs text-gray-300">
                {currentTime.toLocaleDateString('pt-BR')}
              </div>
            </div>
          </div>

          {/* Bottom Row - Balance and Actions */}
          <div className="flex items-center justify-between">
            {/* Saldo Mobile */}
            <div className="balance-card border border-slate-600 rounded-lg px-3 py-2 flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-400" />
              <span className="text-white font-bold text-base">
                {formatCurrency(usuario?.saldo || 0)}
              </span>
            </div>

            {/* Actions Mobile */}
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => setIsWalletModalOpen(true)}
                size="sm"
                className="deposit-button text-white px-4 py-2"
              >
                <Plus className="h-4 w-4 mr-1" />
                Depósito
              </Button>

              {usuario && (
                <div className="relative">
                  <Button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    variant="ghost"
                    size="sm"
                    className="text-white hover:bg-slate-700 p-2"
                  >
                    <ChevronDown className="h-4 w-4" />
                  </Button>

                  {/* Mobile Dropdown */}
                  {showUserMenu && (
                    <div className="user-menu-dropdown absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                      <button
                        onClick={() => {
                          setShowUserMenu(false)
                          onSearchClick?.()
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <Search className="h-4 w-4" />
                        <span>Buscar</span>
                      </button>
                      <button
                        onClick={() => {
                          setShowUserMenu(false)
                          onLogout?.()
                        }}
                        className="w-full px-3 py-2 text-left text-red-600 hover:bg-red-50 flex items-center space-x-2 text-sm"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Sair</span>
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Modal da Carteira */}
      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
        usuario={usuario}
        onSaldoUpdate={onSaldoUpdate}
      />
    </header>
  )
}

// Componente de Status Badge para diferentes estados
export function StatusBadge({ status }: { status: string }) {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pago':
        return { color: 'bg-green-500', text: 'Pago' }
      case 'pendente':
        return { color: 'bg-yellow-500', text: 'Pendente' }
      case 'expirado':
        return { color: 'bg-red-500', text: 'Expirado' }
      case 'cancelado':
        return { color: 'bg-gray-500', text: 'Cancelado' }
      default:
        return { color: 'bg-gray-500', text: status }
    }
  }

  const config = getStatusConfig(status)

  return (
    <Badge className={`${config.color} text-white text-xs px-2 py-1`}>
      {config.text}
    </Badge>
  )
}
