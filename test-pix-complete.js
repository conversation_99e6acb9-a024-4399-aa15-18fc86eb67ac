// Teste completo do sistema PIX
const BASE_URL = 'http://localhost:3000'

async function testPixSystem() {
  console.log('🧪 Iniciando teste completo do sistema PIX...\n')

  try {
    // 1. Testar API PIX diretamente
    console.log('1️⃣ Testando API PIX diretamente...')
    const pixResponse = await fetch(`${BASE_URL}/api/pix/qrcode`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        value: 25.00,
        client_name: 'Guilherme Teste',
        client_email: '<EMAIL>',
        client_document: '12345678901',
        qrcode_image: true
      })
    })

    if (!pixResponse.ok) {
      const errorText = await pixResponse.text()
      console.error('❌ Erro na API PIX:', pixResponse.status, errorText)
      throw new Error(`API PIX falhou: ${pixResponse.status}`)
    }

    const pixData = await pixResponse.json()
    console.log('✅ API PIX funcionando:', {
      success: pixData.success,
      transaction_id: pixData.transaction_id,
      has_qr_code: !!pixData.qr_code_value,
      has_image: !!pixData.qrcode_image
    })

    // 2. Testar criação de depósito
    console.log('\n2️⃣ Testando criação de depósito...')
    const depositResponse = await fetch(`${BASE_URL}/api/wallet/deposit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: 585,
        valor: 50.00,
        client_name: 'Guilherme Teste',
        client_email: '<EMAIL>',
        client_document: '12345678901'
      })
    })

    if (!depositResponse.ok) {
      const errorText = await depositResponse.text()
      console.error('❌ Erro na API de depósito:', depositResponse.status, errorText)
      throw new Error(`API de depósito falhou: ${depositResponse.status}`)
    }

    const depositData = await depositResponse.json()
    console.log('✅ Depósito criado:', {
      success: depositData.success,
      id: depositData.deposito?.id,
      transaction_id: depositData.deposito?.transaction_id,
      valor: depositData.deposito?.valor,
      status: depositData.deposito?.status
    })

    if (!depositData.success) {
      throw new Error('Falha ao criar depósito: ' + depositData.error)
    }

    const transactionId = depositData.deposito.transaction_id

    // 3. Verificar status inicial
    console.log('\n3️⃣ Verificando status inicial...')
    const statusResponse = await fetch(`${BASE_URL}/api/wallet/webhook?transaction_id=${transactionId}`)
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json()
      console.log('📊 Status inicial:', {
        success: statusData.success,
        status: statusData.deposito?.status,
        valor: statusData.deposito?.valor
      })
    } else {
      console.log('⚠️ Não foi possível verificar status inicial')
    }

    // 4. Simular confirmação via webhook
    console.log('\n4️⃣ Simulando confirmação de pagamento...')
    const webhookResponse = await fetch(`${BASE_URL}/api/wallet/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        transaction_id: transactionId,
        order_id: depositData.deposito.order_id,
        status: 'PAID',
        value: 50.00,
        end_to_end_id: `E${Date.now()}${Math.random().toString(36).substr(2, 9)}`
      })
    })

    if (webhookResponse.ok) {
      const webhookData = await webhookResponse.json()
      console.log('🎉 Webhook processado:', {
        success: webhookData.success,
        message: webhookData.message || 'Processado'
      })
    } else {
      const errorText = await webhookResponse.text()
      console.error('❌ Erro no webhook:', webhookResponse.status, errorText)
    }

    // 5. Verificar status final
    console.log('\n5️⃣ Verificando status final...')
    const statusFinalResponse = await fetch(`${BASE_URL}/api/wallet/webhook?transaction_id=${transactionId}`)
    
    if (statusFinalResponse.ok) {
      const statusFinalData = await statusFinalResponse.json()
      console.log('📊 Status final:', {
        success: statusFinalData.success,
        status: statusFinalData.deposito?.status,
        valor: statusFinalData.deposito?.valor
      })
    }

    // 6. Verificar saldo do usuário
    console.log('\n6️⃣ Verificando saldo do usuário...')
    const balanceResponse = await fetch(`${BASE_URL}/api/wallet/balance?user_id=585`)
    
    if (balanceResponse.ok) {
      const balanceData = await balanceResponse.json()
      console.log('💰 Saldo atualizado:', {
        success: balanceData.success,
        saldo: balanceData.usuario?.saldo,
        transacoes: balanceData.transacoes?.length || 0
      })
    }

    console.log('\n✅ Teste completo finalizado!')
    console.log('🎯 Sistema PIX está funcionando!')

  } catch (error) {
    console.error('\n❌ Erro no teste:', error.message)
    console.log('\n🔧 Verifique se o servidor está rodando em http://localhost:3000')
  }
}

// Executar teste
testPixSystem()
