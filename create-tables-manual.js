// Script para criar tabelas manualmente
import { executeQuery, initializeDatabase } from './lib/database-config.js'

async function createTables() {
  try {
    console.log('🏦 Criando tabelas do sistema de carteira...')
    
    await initializeDatabase()

    // 1. Criar tabela de transações de saldo
    console.log('💰 Criando tabela saldo_transacoes...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS saldo_transacoes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        tipo ENUM('deposito', 'compra_bilhete', 'premio', 'estorno', 'bonus') NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        saldo_anterior DECIMAL(10,2) NOT NULL,
        saldo_posterior DECIMAL(10,2) NOT NULL,
        descricao VARCHAR(255) NOT NULL,
        bilhete_id INT NULL,
        transaction_id VARCHAR(255) NULL,
        status ENUM('pendente', 'confirmado', 'cancelado') DEFAULT 'confirmado',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_tipo (tipo),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_transaction_id (transaction_id)
      )
    `)
    console.log('✅ Tabela saldo_transacoes criada')

    // 2. Criar tabela de depósitos PIX
    console.log('💳 Criando tabela depositos_pix...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS depositos_pix (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        transaction_id VARCHAR(255) UNIQUE NOT NULL,
        pix_order_id VARCHAR(255) NULL,
        qr_code_value TEXT NULL,
        qrcode_image LONGTEXT NULL,
        status ENUM('pendente', 'pago', 'expirado', 'cancelado') DEFAULT 'pendente',
        expiration_datetime DATETIME NULL,
        client_name VARCHAR(255) NOT NULL,
        client_email VARCHAR(255) NOT NULL,
        client_document VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      )
    `)
    console.log('✅ Tabela depositos_pix criada')

    // 3. Verificar se a coluna saldo existe na tabela usuarios
    console.log('👤 Verificando coluna saldo na tabela usuarios...')
    try {
      await executeQuery(`
        ALTER TABLE usuarios 
        ADD COLUMN saldo DECIMAL(10,2) DEFAULT 0.00 AFTER status
      `)
      console.log('✅ Coluna saldo adicionada à tabela usuarios')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✅ Coluna saldo já existe na tabela usuarios')
      } else {
        console.error('❌ Erro ao adicionar coluna saldo:', error.message)
      }
    }

    console.log('🎉 Todas as tabelas foram criadas com sucesso!')

  } catch (error) {
    console.error('❌ Erro ao criar tabelas:', error)
  }
}

createTables()
