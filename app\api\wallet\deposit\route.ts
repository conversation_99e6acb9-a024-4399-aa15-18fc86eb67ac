import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const {
      user_id,
      valor,
      client_name,
      client_email,
      client_document
    } = body

    console.log('💰 Iniciando depósito para carteira:', {
      user_id,
      valor,
      client_name,
      client_email,
      client_document
    })

    // Validações
    if (!user_id || !valor || !client_name || !client_email || !client_document) {
      return NextResponse.json({
        success: false,
        error: 'Dados obrigatórios não fornecidos'
      }, { status: 400 })
    }

    if (valor <= 0) {
      return NextResponse.json({
        success: false,
        error: 'Valor deve ser maior que zero'
      }, { status: 400 })
    }

    // Verificar se usuário existe
    const usuario = await executeQuery(`
      SELECT id, nome, email, saldo FROM usuarios WHERE id = ?
    `, [user_id])

    if (!usuario || usuario.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não encontrado'
      }, { status: 404 })
    }

    // Gerar transaction_id único
    const transaction_id = `DEP${Date.now()}${Math.random().toString(36).substr(2, 9)}`

    // Fazer requisição para API PIX
    const pixRequest = {
      value: parseFloat(valor),
      description: `Depósito para carteira - ${client_name}`,
      client_name,
      client_email,
      client_document,
      qrcode_image: true
    }

    console.log('📤 Enviando dados PIX para depósito:', pixRequest)

    const pixResponse = await fetch('/api/pix/qrcode', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(pixRequest)
    })

    if (!pixResponse.ok) {
      const errorData = await pixResponse.json()
      throw new Error(errorData.error || 'Erro ao gerar QR Code PIX')
    }

    const pixData = await pixResponse.json()

    // Salvar depósito no banco
    const depositoResult = await executeQuery(`
      INSERT INTO depositos_pix (
        usuario_id, valor, transaction_id, pix_order_id, 
        qr_code_value, qrcode_image, status, expiration_datetime,
        client_name, client_email, client_document
      ) VALUES (?, ?, ?, ?, ?, ?, 'pendente', ?, ?, ?, ?)
    `, [
      user_id,
      valor,
      transaction_id,
      pixData.order_id || null,
      pixData.qr_code_value || null,
      pixData.qrcode_image || null,
      pixData.expiration_datetime || null,
      client_name,
      client_email,
      client_document
    ])

    const depositoId = (depositoResult as any).insertId

    console.log('✅ Depósito PIX criado:', {
      id: depositoId,
      transaction_id,
      valor,
      status: 'pendente'
    })

    return NextResponse.json({
      success: true,
      message: 'Depósito PIX criado com sucesso',
      deposito: {
        id: depositoId,
        transaction_id,
        valor: parseFloat(valor),
        status: 'pendente',
        qr_code_value: pixData.qr_code_value,
        qrcode_image: pixData.qrcode_image,
        expiration_datetime: pixData.expiration_datetime
      }
    })

  } catch (error) {
    console.error('❌ Erro ao criar depósito:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'user_id é obrigatório'
      }, { status: 400 })
    }

    // Buscar depósitos do usuário
    const depositos = await executeQuery(`
      SELECT 
        id,
        valor,
        transaction_id,
        status,
        created_at,
        updated_at,
        expiration_datetime
      FROM depositos_pix 
      WHERE usuario_id = ?
      ORDER BY created_at DESC
      LIMIT 50
    `, [userId])

    console.log(`📊 ${depositos?.length || 0} depósitos encontrados para usuário ${userId}`)

    // Formatar dados para o frontend
    const depositosFormatados = (depositos || []).map((deposito: any) => ({
      id: deposito.id,
      valor: parseFloat(deposito.valor),
      transaction_id: deposito.transaction_id,
      status: deposito.status,
      data: new Date(deposito.created_at).toLocaleString('pt-BR'),
      expiracao: deposito.expiration_datetime ? 
        new Date(deposito.expiration_datetime).toLocaleString('pt-BR') : null
    }))

    return NextResponse.json({
      success: true,
      depositos: depositosFormatados,
      total: depositosFormatados.length
    })

  } catch (error) {
    console.error('❌ Erro ao buscar depósitos:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      depositos: []
    }, { status: 500 })
  }
}
