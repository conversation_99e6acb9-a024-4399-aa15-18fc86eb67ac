import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    console.log('🔔 Webhook recebido para depósito:', body)

    const {
      order_id,
      transaction_id,
      status,
      value,
      end_to_end_id
    } = body

    if (!order_id && !transaction_id) {
      return NextResponse.json({
        success: false,
        error: 'order_id ou transaction_id é obrigatório'
      }, { status: 400 })
    }

    // Buscar depósito correspondente
    const deposito = await executeQuery(`
      SELECT 
        id, usuario_id, valor, status as current_status,
        transaction_id, pix_order_id, client_name
      FROM depositos_pix 
      WHERE transaction_id = ? OR pix_order_id = ?
      LIMIT 1
    `, [transaction_id || order_id, order_id || transaction_id])

    if (!deposito || deposito.length === 0) {
      console.log('⚠️ Depósito não encontrado para:', { order_id, transaction_id })
      return NextResponse.json({
        success: false,
        error: 'Depósito não encontrado'
      }, { status: 404 })
    }

    const depositoData = deposito[0]
    console.log('📋 Depósito encontrado:', {
      id: depositoData.id,
      usuario_id: depositoData.usuario_id,
      valor: depositoData.valor,
      status_atual: depositoData.current_status
    })

    // Mapear status do webhook para status do banco
    let novoStatus = status
    if (status === 'approved' || status === 'paid' || status === 'PAID') {
      novoStatus = 'pago'
    } else if (status === 'expired' || status === 'EXPIRED') {
      novoStatus = 'expirado'
    } else if (status === 'cancelled' || status === 'CANCELLED') {
      novoStatus = 'cancelado'
    }

    // Se o status não mudou, não fazer nada
    if (depositoData.current_status === novoStatus) {
      console.log('✅ Status já está atualizado:', novoStatus)
      return NextResponse.json({
        success: true,
        message: 'Status já atualizado',
        status: novoStatus
      })
    }

    // Iniciar transação
    await executeQuery('START TRANSACTION')

    try {
      // Atualizar status do depósito
      await executeQuery(`
        UPDATE depositos_pix 
        SET status = ?, updated_at = NOW()
        WHERE id = ?
      `, [novoStatus, depositoData.id])

      // Se o pagamento foi aprovado, creditar na carteira
      if (novoStatus === 'pago') {
        console.log('💰 Creditando valor na carteira do usuário...')

        // Buscar saldo atual do usuário
        const usuario = await executeQuery(`
          SELECT saldo FROM usuarios WHERE id = ?
        `, [depositoData.usuario_id])

        if (!usuario || usuario.length === 0) {
          throw new Error('Usuário não encontrado')
        }

        const saldoAnterior = parseFloat(usuario[0].saldo) || 0
        const valorDeposito = parseFloat(depositoData.valor)
        const saldoPosterior = saldoAnterior + valorDeposito

        // Atualizar saldo do usuário
        await executeQuery(`
          UPDATE usuarios SET saldo = ? WHERE id = ?
        `, [saldoPosterior, depositoData.usuario_id])

        // Registrar transação de saldo
        await executeQuery(`
          INSERT INTO saldo_transacoes (
            usuario_id, tipo, valor, saldo_anterior, saldo_posterior,
            descricao, transaction_id, status
          ) VALUES (?, 'deposito', ?, ?, ?, ?, ?, 'confirmado')
        `, [
          depositoData.usuario_id,
          valorDeposito,
          saldoAnterior,
          saldoPosterior,
          `Depósito PIX - ${depositoData.client_name}`,
          depositoData.transaction_id
        ])

        console.log('✅ Saldo creditado:', {
          usuario_id: depositoData.usuario_id,
          valor: valorDeposito,
          saldoAnterior,
          saldoPosterior
        })
      }

      // Confirmar transação
      await executeQuery('COMMIT')

      console.log('✅ Webhook processado com sucesso:', {
        deposito_id: depositoData.id,
        status_anterior: depositoData.current_status,
        status_novo: novoStatus,
        creditado: novoStatus === 'pago'
      })

      return NextResponse.json({
        success: true,
        message: 'Webhook processado com sucesso',
        deposito_id: depositoData.id,
        status_anterior: depositoData.current_status,
        status_novo: novoStatus,
        creditado: novoStatus === 'pago'
      })

    } catch (error) {
      await executeQuery('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('❌ Erro ao processar webhook de depósito:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}

// Endpoint para verificar status de depósito manualmente
export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const transactionId = searchParams.get('transaction_id')

    if (!transactionId) {
      return NextResponse.json({
        success: false,
        error: 'transaction_id é obrigatório'
      }, { status: 400 })
    }

    // Buscar depósito
    const deposito = await executeQuery(`
      SELECT 
        d.id, d.usuario_id, d.valor, d.status, d.transaction_id,
        d.created_at, d.updated_at,
        u.nome as usuario_nome, u.email as usuario_email
      FROM depositos_pix d
      JOIN usuarios u ON d.usuario_id = u.id
      WHERE d.transaction_id = ?
      LIMIT 1
    `, [transactionId])

    if (!deposito || deposito.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Depósito não encontrado'
      }, { status: 404 })
    }

    const depositoData = deposito[0]

    return NextResponse.json({
      success: true,
      deposito: {
        id: depositoData.id,
        usuario_id: depositoData.usuario_id,
        usuario_nome: depositoData.usuario_nome,
        usuario_email: depositoData.usuario_email,
        valor: parseFloat(depositoData.valor),
        status: depositoData.status,
        transaction_id: depositoData.transaction_id,
        created_at: new Date(depositoData.created_at).toLocaleString('pt-BR'),
        updated_at: new Date(depositoData.updated_at).toLocaleString('pt-BR')
      }
    })

  } catch (error) {
    console.error('❌ Erro ao consultar depósito:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}
